# 图表视图优化说明

## 问题描述
原始图表在正常添加数据时可以移动和缩放，但图表刷新时，移动了一瞬间又被回弹到添加数据的地方，影响用户体验。

## 问题根本原因
1. `_graph_auto_scale_flag`变量被注释掉，没有正确初始化
2. 图表在每次数据更新时都会强制设置X轴范围到最新数据位置（第4497行）
3. 没有检测用户是否正在手动操作图表，导致用户交互被数据更新覆盖

## 优化方案

### 1. 修复自动缩放标志初始化
```python
# 设置自动缩放标志 - 控制图表是否自动跟随最新数据
self._graph_auto_scale_flag = True  # 默认开启自动跟随

# 用户交互状态标志 - 检测用户是否正在手动操作图表
self._user_interacting = False
self._last_user_interaction_time = 0
```

### 2. 添加用户交互检测
```python
# 连接用户交互事件监听
plot_item.vb.sigRangeChangedManually.connect(self.on_user_range_changed)
plot_item.vb.sigRangeChanged.connect(self.on_range_changed)
```

### 3. 智能X轴范围控制
```python
# X轴范围智能跟随 - 只在自动缩放模式且用户未交互时自动跟随最新数据
should_auto_follow = (
    self._graph_auto_scale_flag and  # 自动缩放模式开启
    (not hasattr(self, '_user_interacting') or not self._user_interacting)  # 用户未在交互
)

if should_auto_follow:
    # 自动跟随最新数据
    self.ui.widgetGraph1.setXRange(time1[start_index1], time1[to_index1 - 1])
else:
    # 用户交互模式 - 保持当前视图范围不变
    pass
```

### 4. 用户交互超时机制
- 用户手动操作图表后，3秒内图表不会自动跟随最新数据
- 3秒后自动恢复跟随模式（如果自动缩放开启）

### 5. 按钮状态管理
- "适应"模式：图表自动跟随最新数据
- "手动"模式：图表保持用户设置的视图范围
- 切换到"适应"模式时立即清除用户交互状态

## 优化效果

### 用户体验改进
1. **智能跟随**：默认情况下图表自动跟随最新数据
2. **交互保护**：用户手动移动/缩放时，图表不会立即回弹
3. **自动恢复**：用户停止交互3秒后，图表自动恢复跟随模式
4. **手动控制**：点击"手动"按钮可完全禁用自动跟随

### 技术改进
1. **状态管理**：正确初始化和管理图表状态标志
2. **事件监听**：准确检测用户交互行为
3. **超时机制**：避免图表永久停留在手动模式
4. **性能优化**：减少不必要的视图范围更新

## 使用方法

### 自动模式（默认）
- 图表自动跟随最新数据显示
- 用户可以临时移动/缩放查看历史数据
- 3秒后自动恢复跟随最新数据

### 手动模式
- 点击"适应"按钮切换到"手动"模式
- 图表完全由用户控制，不会自动跟随数据
- 点击"手动"按钮切换回"适应"模式

## 代码修改位置

1. **初始化部分**（第4049-4054行）：添加状态标志初始化
2. **事件监听**（第4081-4084行）：添加用户交互事件监听
3. **回调方法**（第4117-4136行）：添加用户交互检测方法
4. **X轴控制**（第4502-4518行）：修改X轴范围设置逻辑
5. **按钮控制**（第4885-4895行）：改进按钮状态管理

## 兼容性
- 保持原有API不变
- 向后兼容现有功能
- 不影响其他图表功能（Y轴自动调整、缩略图等）
